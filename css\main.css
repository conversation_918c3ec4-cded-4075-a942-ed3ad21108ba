/* Ullens Design System CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-blue: #4A90E2;
    --primary-orange: #F5A623;
    --primary-purple: #7B68EE;
    --primary-teal: #50C4B7;
    --primary-pink: #E91E63;
    --primary-green: #4CAF50;
    --primary-red: #F44336;
    --accent-maroon: #8B0000;
    --text-dark: #333333;
    --text-light: #666666;
    --background-light: #FFFFFF;
    --background-gray: #F8F9FA;
}

body {
    font-family: 'Arial', 'Helvetica', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--background-light);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: var(--background-light);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-maroon);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-blue);
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('../assets/images/modern-school-1.jpg');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    margin-top: 80px;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    background: var(--primary-blue);
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background: #357ABD;
    transform: translateY(-2px);
}

.btn-orange { background: var(--primary-orange); }
.btn-orange:hover { background: #E6941A; }

.btn-purple { background: var(--primary-purple); }
.btn-purple:hover { background: #6A5ACD; }

.btn-teal { background: var(--primary-teal); }
.btn-teal:hover { background: #45B7AA; }

.btn-pink { background: var(--primary-pink); }
.btn-pink:hover { background: #C2185B; }

/* Navigation Cards */
.nav-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 4rem 0;
}

.nav-card {
    background: var(--background-light);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.nav-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.nav-card-header {
    height: 200px;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.nav-card-content {
    padding: 1.5rem;
}

.nav-card-content h3 {
    margin-bottom: 0.5rem;
    color: var(--accent-maroon);
}

/* Color-coded cards */
.card-blue .nav-card-header { background: linear-gradient(135deg, var(--primary-blue), #357ABD); }
.card-orange .nav-card-header { background: linear-gradient(135deg, var(--primary-orange), #E6941A); }
.card-purple .nav-card-header { background: linear-gradient(135deg, var(--primary-purple), #6A5ACD); }
.card-teal .nav-card-header { background: linear-gradient(135deg, var(--primary-teal), #45B7AA); }
.card-pink .nav-card-header { background: linear-gradient(135deg, var(--primary-pink), #C2185B); }
.card-green .nav-card-header { background: linear-gradient(135deg, var(--primary-green), #45A049); }
.card-red .nav-card-header { background: linear-gradient(135deg, var(--primary-red), #D32F2F); }

/* Content Sections */
.section {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--accent-maroon);
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.content-card {
    background: var(--background-light);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.content-card h3 {
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: var(--primary-blue);
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    display: block;
    margin-bottom: 0.5rem;
}

.footer-section a:hover {
    color: var(--primary-blue);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .nav-menu {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-cards {
        grid-template-columns: 1fr;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
}

