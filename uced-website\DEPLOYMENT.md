# UCED Website Deployment Guide

This guide provides instructions for deploying the UCED website to various hosting platforms.

## 📋 Pre-Deployment Checklist

### ✅ Content Review
- [ ] All content is accurate and up-to-date
- [ ] Contact information is correct
- [ ] Program details are current
- [ ] Links are working properly

### ✅ Technical Review
- [ ] All images are optimized and compressed
- [ ] CSS and JavaScript are minified (if needed)
- [ ] Meta tags are properly configured
- [ ] Favicon and touch icons are in place
- [ ] Social media images are uploaded

### ✅ Testing
- [ ] Test on multiple browsers (Chrome, Firefox, Safari, Edge)
- [ ] Test on mobile devices
- [ ] Test accessibility with screen readers
- [ ] Validate HTML and CSS
- [ ] Check page load speed

## 🚀 Deployment Options

### Option 1: Static Hosting (Recommended)

#### Netlify (Free/Paid)
1. Create account at [netlify.com](https://netlify.com)
2. Drag and drop the `uced-website` folder to Netlify
3. Configure custom domain (uced.ullens.edu.np)
4. Enable HTTPS (automatic)
5. Set up form handling if needed

#### Vercel (Free/Paid)
1. Create account at [vercel.com](https://vercel.com)
2. Import project from Git or upload files
3. Configure domain settings
4. Deploy with automatic HTTPS

#### GitHub Pages (Free)
1. Create GitHub repository
2. Upload files to repository
3. Enable GitHub Pages in repository settings
4. Configure custom domain

### Option 2: Traditional Web Hosting

#### Shared Hosting
1. Purchase hosting plan with cPanel access
2. Upload files via FTP or File Manager
3. Configure domain DNS settings
4. Set up SSL certificate

#### VPS/Dedicated Server
1. Set up web server (Apache/Nginx)
2. Configure domain and SSL
3. Upload files to web directory
4. Set proper file permissions

## 🔧 Configuration Steps

### 1. Domain Setup
```
Domain: uced.ullens.edu.np
DNS Records:
- A Record: @ → [Server IP]
- CNAME: www → uced.ullens.edu.np
```

### 2. SSL Certificate
- Use Let's Encrypt for free SSL
- Most hosting providers offer automatic SSL
- Ensure HTTPS redirect is enabled

### 3. Performance Optimization
```apache
# .htaccess for Apache servers
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

### 4. Security Headers
```apache
# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=********; includeSubDomains; preload"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
```

## 📊 Analytics Setup

### Google Analytics
1. Create Google Analytics account
2. Add tracking code to `index.html` before `</head>`:
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### Google Search Console
1. Verify domain ownership
2. Submit sitemap.xml
3. Monitor search performance

## 🔍 SEO Setup

### 1. Create Sitemap
Create `sitemap.xml`:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://uced.ullens.edu.np/</loc>
    <lastmod>2025-01-19</lastmod>
    <changefreq>monthly</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>
```

### 2. Create robots.txt
```
User-agent: *
Allow: /
Sitemap: https://uced.ullens.edu.np/sitemap.xml
```

## 📧 Contact Form Setup

If adding a contact form, consider:
- Netlify Forms (if using Netlify)
- Formspree for static sites
- EmailJS for client-side form handling

## 🔄 Maintenance

### Regular Updates
- Update content as programs change
- Review and update contact information
- Monitor website performance
- Update dependencies if any

### Backup Strategy
- Regular backups of website files
- Version control with Git
- Database backups (if applicable)

### Monitoring
- Set up uptime monitoring
- Monitor page load speeds
- Check for broken links regularly
- Review analytics data

## 🆘 Troubleshooting

### Common Issues
1. **Images not loading**: Check file paths and permissions
2. **CSS not applying**: Verify file paths and MIME types
3. **Mobile layout issues**: Test responsive breakpoints
4. **Slow loading**: Optimize images and enable compression

### Support Contacts
- Hosting provider support
- Domain registrar support
- UCED technical team

## 📱 Post-Deployment Testing

### Functionality Tests
- [ ] All navigation links work
- [ ] Slider functions properly
- [ ] Mobile menu operates correctly
- [ ] Contact information is clickable
- [ ] Social media links work

### Performance Tests
- [ ] Page load speed < 3 seconds
- [ ] Images load properly
- [ ] No console errors
- [ ] Responsive design works on all devices

### SEO Tests
- [ ] Meta tags are present
- [ ] Structured data validates
- [ ] Social media previews work
- [ ] Search engines can crawl the site

---

*For technical support with deployment, contact the UCED development team.*
