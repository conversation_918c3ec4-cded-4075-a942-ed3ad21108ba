/* UCED Website - Responsive Styles */

/* Mobile First Approach - Base styles are for mobile */

/* Mobile Styles (up to 767px) */
@media (max-width: 767px) {
    .container {
        padding: 0 1rem;
    }

    /* Navigation */
    .nav {
        padding: 1rem;
    }

    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        flex-direction: column;
        gap: 0;
        box-shadow: var(--shadow-medium);
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-menu li {
        border-bottom: 1px solid var(--border-color);
    }

    .nav-menu li:last-child {
        border-bottom: none;
    }

    .nav-link {
        padding: 1rem;
        display: block;
        border-radius: 0;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    /* Hero Section */
    .hero {
        min-height: 60vh;
        padding: 4rem 1rem 2rem;
        margin-top: 70px;
    }

    .hero-content h1 {
        font-size: 2rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-description {
        font-size: 0.9rem;
    }

    /* Sections */
    .section {
        padding: 3rem 0;
    }

    .section-title {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    /* About Section */
    .about-content {
        margin-bottom: 3rem;
    }

    .about-text p {
        font-size: 1rem;
    }

    /* Statistics */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-top: 2rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    /* Slider */
    .program-slider {
        margin: 0 -1rem 3rem;
        border-radius: 0;
    }

    .slide {
        padding: 2rem 1rem;
    }

    .slide h3 {
        font-size: 1.8rem;
        line-height: 1.3;
    }

    .slide-meta {
        font-size: 1rem;
    }

    .slide p {
        font-size: 0.95rem;
    }

    .slider-prev,
    .slider-next {
        width: 44px;
        height: 44px;
        font-size: 1.2rem;
    }

    .slider-prev {
        left: 10px;
    }

    .slider-next {
        right: 10px;
    }

    .slider-dot {
        width: 12px;
        height: 12px;
        margin: 0 4px;
        min-width: 44px;
        min-height: 44px;
    }

    /* Content Grid */
    .content-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .content-card {
        padding: 1.5rem;
    }

    /* Additional Programs */
    .additional-programs {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-top: 3rem;
    }

    .program-card {
        padding: 1.5rem;
    }

    /* Timeline */
    .timeline-desktop {
        display: none;
    }

    .timeline-mobile {
        display: block;
    }

    .mobile-timeline-item h3 {
        font-size: 1.2rem;
    }

    /* Footer */
    .footer {
        padding: 2rem 0 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }

    .footer-section {
        margin-bottom: 1rem;
    }

    /* Buttons */
    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* Tablet Styles (768px to 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .container {
        padding: 0 2rem;
    }

    /* Hero */
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    /* Statistics */
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }

    /* Slider */
    .slide {
        padding: 3rem 2rem;
    }

    .slide h3 {
        font-size: 2.2rem;
    }

    /* Content Grid */
    .content-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    /* Additional Programs */
    .additional-programs {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Timeline */
    .timeline-desktop {
        display: block;
    }

    .timeline-mobile {
        display: none;
    }

    /* Footer */
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        text-align: left;
    }
}

/* Desktop Styles (1024px and up) */
@media (min-width: 1024px) {
    /* Hero */
    .hero {
        min-height: 70vh;
    }

    .hero-content h1 {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1.3rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    /* Navigation */
    .nav-menu {
        justify-content: flex-end;
    }

    .nav-link {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }

    /* Slider */
    .slide {
        padding: 4rem 3rem;
    }

    .slide h3 {
        font-size: 2.5rem;
    }

    .slide-meta {
        font-size: 1.3rem;
    }

    .slide p {
        font-size: 1.1rem;
    }

    /* Content Grid */
    .content-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2.5rem;
    }

    /* Timeline */
    .timeline-desktop {
        display: block;
    }

    .timeline-mobile {
        display: none;
    }

    /* Footer */
    .footer-content {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .container {
        padding: 0 3rem;
    }

    .hero-content h1 {
        font-size: 3.5rem;
    }

    .slide h3 {
        font-size: 2.8rem;
    }

    .slide p {
        font-size: 1.2rem;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero {
        background-attachment: scroll; /* Better performance on high DPI */
    }
}

/* Reduced Motion for Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-dark: #ffffff;
        --text-light: #cccccc;
        --background-light: #1a1a1a;
        --background-gray: #2d2d2d;
        --border-color: #444444;
    }

    .content-card,
    .program-card,
    .stat-card {
        background: #2d2d2d;
        color: #ffffff;
    }

    .header {
        background: #1a1a1a;
    }
}

/* Print Styles */
@media print {
    .slider-prev,
    .slider-next,
    .slider-dot,
    .nav-menu,
    .mobile-menu-toggle {
        display: none !important;
    }

    .slide {
        break-inside: avoid;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    .hero {
        background: none !important;
        color: black !important;
    }

    .section {
        page-break-inside: avoid;
    }
}
