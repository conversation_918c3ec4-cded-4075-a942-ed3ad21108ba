<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UCED - Ullens Center for Educator Development</title>
    <link rel="stylesheet" href="../css/main.css">
</head>
<body>
    <header class="header">
        <nav class="nav container">
            <div class="logo">
                <a href="../index.html" style="text-decoration: none; color: inherit;">UCED</a>
            </div>
            <ul class="nav-menu">
                <li><a href="#about" class="nav-link">About UCED</a></li>
                <li><a href="#programs" class="nav-link">Training Programs</a></li>
                <li><a href="#partnerships" class="nav-link">Partnerships</a></li>
                <li><a href="#community" class="nav-link">Community Schools</a></li>
                <li><a href="#enrollment" class="nav-link">Enrollment</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="hero" style="background: linear-gradient(135deg, #4CAF50, #66BB6A);">
            <div class="hero-content">
                <h1>Ullens Center for Educator Development</h1>
                <p>Dedicated Teacher Training Unit of Ullens Education Foundation</p>
                <p>Operating in collaboration with Kathmandu University • Transformational Leadership • Progressive Education</p>
                <a href="#about" class="btn btn-green">Discover UCED</a>
            </div>
        </section>

        <section id="about" class="section">
            <div class="container">
                <div style="max-width: 800px; margin: 0 auto; text-align: center;">
                    <h2 class="section-title">About UCED</h2>
                    <div style="font-size: 1.2rem; line-height: 1.8; color: #555; margin-bottom: 3rem;">
                        <p style="margin-bottom: 1.5rem; animation: fadeInUp 0.8s ease-out;">
                            <strong>UCED is the dedicated teacher training unit of the Ullens Education Foundation</strong>, operating in collaboration with Kathmandu University. We provide high-quality training in transformational leadership and progressive education to enhance the professional capacity of educators across Nepal.
                        </p>
                        <p style="margin-bottom: 1.5rem; animation: fadeInUp 0.8s ease-out 0.2s; animation-fill-mode: both;">
                            We serve educators within the Ullens School network—Khumaltar and Kathmandu—and from the broader teaching community, including government school teachers, private institution educators, school leaders, and aspiring administrators.
                        </p>
                        <p style="margin-bottom: 2rem; animation: fadeInUp 0.8s ease-out 0.4s; animation-fill-mode: both;">
                            All training programs are <strong>academically accredited by Kathmandu University School of Education (KUSOED)</strong>, ensuring both rigor and relevance in educator development with participants earning valuable academic credits.
                        </p>
                    </div>
                </div>

                <!-- Animated Statistics -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; margin-top: 3rem;">
                    <div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #E8F5E8, #F1F8E9); border-radius: 15px; animation: slideInLeft 0.8s ease-out;">
                        <div style="font-size: 3rem; font-weight: bold; color: #4CAF50; margin-bottom: 0.5rem;" class="counter" data-target="8">0</div>
                        <div style="font-size: 1.1rem; color: #666;">Training Programs</div>
                    </div>
                    <div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #E3F2FD, #F0F8FF); border-radius: 15px; animation: slideInUp 0.8s ease-out 0.2s; animation-fill-mode: both;">
                        <div style="font-size: 3rem; font-weight: bold; color: #2196F3; margin-bottom: 0.5rem;" class="counter" data-target="6">0</div>
                        <div style="font-size: 1.1rem; color: #666;">Max Academic Credits</div>
                    </div>
                    <div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #FFF3E0, #FFF8E1); border-radius: 15px; animation: slideInRight 0.8s ease-out 0.4s; animation-fill-mode: both;">
                        <div style="font-size: 3rem; font-weight: bold; color: #FF9800; margin-bottom: 0.5rem;" class="counter" data-target="96">0</div>
                        <div style="font-size: 1.1rem; color: #666;">Max Training Hours</div>
                    </div>
                </div>
            </div>
        </section>

        <section id="programs" class="section" style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); padding: 4rem 0;">
            <div class="container">
                <h2 class="section-title" style="margin-bottom: 3rem;">Teacher Training Programs</h2>

                <!-- Program Slider -->
                <div class="program-slider" style="position: relative; overflow: hidden; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                    <div class="slider-container" style="display: flex; transition: transform 0.5s ease-in-out;">

                        <!-- Slide 1 -->
                        <div class="slide" style="min-width: 100%; padding: 3rem; background: linear-gradient(135deg, #4CAF50, #66BB6A); color: white; text-align: center;">
                            <div style="max-width: 600px; margin: 0 auto;">
                                <h3 style="font-size: 2.5rem; margin-bottom: 1rem; animation: slideInDown 0.6s ease-out;">Progressive Pedagogy in Practice</h3>
                                <div style="font-size: 1.3rem; margin-bottom: 1.5rem; opacity: 0.9;">32 hours (7 days) • 2 Academic Credits</div>
                                <p style="font-size: 1.1rem; line-height: 1.6; animation: fadeIn 0.8s ease-out 0.3s; animation-fill-mode: both;">Dynamic short-course training for educators seeking to embrace progressive education methods. Ideal for both new and experienced teachers, this program equips participants with essential skills to effectively teach 21st-century learners.</p>
                            </div>
                        </div>

                        <!-- Slide 2 -->
                        <div class="slide" style="min-width: 100%; padding: 3rem; background: linear-gradient(135deg, #2196F3, #42A5F5); color: white; text-align: center;">
                            <div style="max-width: 600px; margin: 0 auto;">
                                <h3 style="font-size: 2.5rem; margin-bottom: 1rem;">Inside Impact: Continuous Development</h3>
                                <div style="font-size: 1.3rem; margin-bottom: 1.5rem; opacity: 0.9;">60 hours • 3 Academic Credits</div>
                                <p style="font-size: 1.1rem; line-height: 1.6;">Comprehensive course exclusively for in-house teachers, delving into theoretical and philosophical foundations of progressive education while offering practical strategies for dynamic, student-centered classrooms.</p>
                            </div>
                        </div>

                        <!-- Slide 3 -->
                        <div class="slide" style="min-width: 100%; padding: 3rem; background: linear-gradient(135deg, #FF9800, #FFB74D); color: white; text-align: center;">
                            <div style="max-width: 600px; margin: 0 auto;">
                                <h3 style="font-size: 2.5rem; margin-bottom: 1rem;">Becoming a Progressive Educator</h3>
                                <div style="font-size: 1.3rem; margin-bottom: 1.5rem; opacity: 0.9;">96 hours (3 months) • 6 Academic Credits</div>
                                <p style="font-size: 1.1rem; line-height: 1.6;">Intensive hands-on training with real classroom observation and practice at Ullens School (K-12). Participants gain practical tools for active, student-centered learning and holistic development of learners.</p>
                            </div>
                        </div>

                        <!-- Slide 4 -->
                        <div class="slide" style="min-width: 100%; padding: 3rem; background: linear-gradient(135deg, #9C27B0, #BA68C8); color: white; text-align: center;">
                            <div style="max-width: 600px; margin: 0 auto;">
                                <h3 style="font-size: 2.5rem; margin-bottom: 1rem;">Leading the Change: Leadership Training</h3>
                                <div style="font-size: 1.3rem; margin-bottom: 1.5rem; opacity: 0.9;">16 hours (4 days) • 1 Academic Credit</div>
                                <p style="font-size: 1.1rem; line-height: 1.6;">Designed for visionary school leaders, offering progressive education philosophy and practical leadership skills to manage academic and non-academic challenges with innovation, empathy, and impact.</p>
                            </div>
                        </div>

                    </div>

                    <!-- Navigation Dots -->
                    <div style="text-align: center; margin-top: 2rem;">
                        <button class="slider-dot active" onclick="currentSlide(1)" style="width: 15px; height: 15px; border-radius: 50%; border: none; background: #4CAF50; margin: 0 5px; cursor: pointer; transition: all 0.3s;"></button>
                        <button class="slider-dot" onclick="currentSlide(2)" style="width: 15px; height: 15px; border-radius: 50%; border: none; background: #ccc; margin: 0 5px; cursor: pointer; transition: all 0.3s;"></button>
                        <button class="slider-dot" onclick="currentSlide(3)" style="width: 15px; height: 15px; border-radius: 50%; border: none; background: #ccc; margin: 0 5px; cursor: pointer; transition: all 0.3s;"></button>
                        <button class="slider-dot" onclick="currentSlide(4)" style="width: 15px; height: 15px; border-radius: 50%; border: none; background: #ccc; margin: 0 5px; cursor: pointer; transition: all 0.3s;"></button>
                    </div>

                    <!-- Navigation Arrows -->
                    <button class="slider-prev" onclick="plusSlides(-1)" style="position: absolute; top: 50%; left: 20px; transform: translateY(-50%); background: rgba(255,255,255,0.8); border: none; border-radius: 50%; width: 50px; height: 50px; font-size: 1.5rem; cursor: pointer; transition: all 0.3s;">‹</button>
                    <button class="slider-next" onclick="plusSlides(1)" style="position: absolute; top: 50%; right: 20px; transform: translateY(-50%); background: rgba(255,255,255,0.8); border: none; border-radius: 50%; width: 50px; height: 50px; font-size: 1.5rem; cursor: pointer; transition: all 0.3s;">›</button>
                </div>

                <!-- Additional Programs Grid -->
                <div style="margin-top: 4rem; display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                    <div style="padding: 2rem; background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 5px 15px rgba(0,0,0,0.1)'">
                        <h3 style="color: #4CAF50; margin-bottom: 1rem;">Customized Short Courses</h3>
                        <div style="font-weight: bold; color: #666; margin-bottom: 1rem;">Flexible Duration • Tailored Credits</div>
                        <p style="color: #555; line-height: 1.6;">Range of short courses focusing on philosophical and theoretical foundations, tailored by grade level and subject area. Schools can co-design training modules based on specific needs.</p>
                    </div>
                    <div style="padding: 2rem; background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 5px 15px rgba(0,0,0,0.1)'">
                        <h3 style="color: #2196F3; margin-bottom: 1rem;">School Reform Initiative (SRI)</h3>
                        <div style="font-weight: bold; color: #666; margin-bottom: 1rem;">Long-term Program • Comprehensive Support</div>
                        <p style="color: #555; line-height: 1.6;">Comprehensive program designed to bring meaningful and sustainable change to schools, transforming the entire school ecosystem and fostering student-centered learning environments.</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="community" class="section" style="background: linear-gradient(135deg, #f1f8e9, #e8f5e8); padding: 4rem 0;">
            <div class="container">
                <h2 class="section-title" style="margin-bottom: 3rem;">Community Impact & Additional Programs</h2>

                <!-- Impact Timeline -->
                <div style="max-width: 800px; margin: 0 auto; position: relative;">
                    <!-- Timeline Line -->
                    <div style="position: absolute; left: 50%; top: 0; bottom: 0; width: 4px; background: linear-gradient(to bottom, #4CAF50, #66BB6A); transform: translateX(-50%);"></div>

                    <!-- Timeline Item 1 -->
                    <div style="display: flex; align-items: center; margin-bottom: 3rem; animation: slideInLeft 0.8s ease-out;">
                        <div style="flex: 1; text-align: right; padding-right: 2rem;">
                            <h3 style="color: #4CAF50; font-size: 1.5rem; margin-bottom: 0.5rem;">Empowering Public Education</h3>
                            <p style="color: #666; font-weight: bold; margin-bottom: 1rem;">Community School Support Program</p>
                            <p style="color: #555; line-height: 1.6;">UCED actively partners with community schools across various regions to strengthen public education. We build capacity of teachers, school leaders, and support staff through targeted training, mentorship, and ongoing support.</p>
                        </div>
                        <div style="width: 20px; height: 20px; background: #4CAF50; border-radius: 50%; border: 4px solid white; box-shadow: 0 0 0 4px #4CAF50; z-index: 1; position: relative;"></div>
                        <div style="flex: 1; padding-left: 2rem;"></div>
                    </div>

                    <!-- Timeline Item 2 -->
                    <div style="display: flex; align-items: center; margin-bottom: 3rem; animation: slideInRight 0.8s ease-out 0.2s; animation-fill-mode: both;">
                        <div style="flex: 1; padding-right: 2rem;"></div>
                        <div style="width: 20px; height: 20px; background: #2196F3; border-radius: 50%; border: 4px solid white; box-shadow: 0 0 0 4px #2196F3; z-index: 1; position: relative;"></div>
                        <div style="flex: 1; text-align: left; padding-left: 2rem;">
                            <h3 style="color: #2196F3; font-size: 1.5rem; margin-bottom: 0.5rem;">Training of Trainers (ToT)</h3>
                            <p style="color: #666; font-weight: bold; margin-bottom: 1rem;">Short-term & Long-term Programs</p>
                            <p style="color: #555; line-height: 1.6;">Designed to develop skilled teacher trainers who can contribute meaningfully to the academic field through progressive pedagogy and the Ullens approach to education. Available for experienced educators and aspiring trainers.</p>
                        </div>
                    </div>

                    <!-- Timeline Item 3 -->
                    <div style="display: flex; align-items: center; margin-bottom: 3rem; animation: slideInLeft 0.8s ease-out 0.4s; animation-fill-mode: both;">
                        <div style="flex: 1; text-align: right; padding-right: 2rem;">
                            <h3 style="color: #FF9800; font-size: 1.5rem; margin-bottom: 0.5rem;">Systemic Change Focus</h3>
                            <p style="color: #666; font-weight: bold; margin-bottom: 1rem;">National Education Quality</p>
                            <p style="color: #555; line-height: 1.6;">We believe that national education quality cannot be elevated without meaningful improvement in community schools. Our programs create sustainable, systemic change that benefits the entire education system.</p>
                        </div>
                        <div style="width: 20px; height: 20px; background: #FF9800; border-radius: 50%; border: 4px solid white; box-shadow: 0 0 0 4px #FF9800; z-index: 1; position: relative;"></div>
                        <div style="flex: 1; padding-left: 2rem;"></div>
                    </div>

                    <!-- Timeline Item 4 -->
                    <div style="display: flex; align-items: center; animation: slideInRight 0.8s ease-out 0.6s; animation-fill-mode: both;">
                        <div style="flex: 1; padding-right: 2rem;"></div>
                        <div style="width: 20px; height: 20px; background: #9C27B0; border-radius: 50%; border: 4px solid white; box-shadow: 0 0 0 4px #9C27B0; z-index: 1; position: relative;"></div>
                        <div style="flex: 1; text-align: left; padding-left: 2rem;">
                            <h3 style="color: #9C27B0; font-size: 1.5rem; margin-bottom: 0.5rem;">Subject-Specific Workshops</h3>
                            <p style="color: #666; font-weight: bold; margin-bottom: 1rem;">Practical Application Focus</p>
                            <p style="color: #555; line-height: 1.6;">UCED offers subject-specific and level-based workshops to help teachers incorporate progressive pedagogy into their lesson planning and delivery, ensuring practical application of theoretical knowledge.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="partnerships" class="section" style="background: var(--background-gray);">
            <div class="container">
                <h2 class="section-title">Academic Partnerships & Accreditation</h2>
                <div class="content-grid">
                    <div class="content-card">
                        <h3>Kathmandu University Collaboration</h3>
                        <p>UCED operates in collaboration with Kathmandu University, ensuring our programs meet the highest academic standards. All training programs are academically accredited by the Kathmandu University School of Education (KUSOED).</p>
                    </div>
                    <div class="content-card">
                        <h3>Academic Credits</h3>
                        <p>Participants in UCED programs earn academic credits from Kathmandu University School of Education, ranging from 1 to 6 credits depending on the program duration and intensity, adding value to their professional development journey.</p>
                    </div>
                    <div class="content-card">
                        <h3>Quality Assurance</h3>
                        <p>Our partnership with Kathmandu University ensures both rigor and relevance in educator development, with programs grounded in research-based practices and progressive educational theory.</p>
                    </div>
                    <div class="content-card">
                        <h3>Ullens Education Foundation</h3>
                        <p>As the dedicated teacher training unit of Ullens Education Foundation, UCED leverages the foundation's extensive experience in progressive education and its network of schools for practical training opportunities.</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="enrollment" class="section">
            <div class="container">
                <h2 class="section-title">Join UCED Programs</h2>
                <div class="content-grid">
                    <div class="content-card">
                        <h3>Individual Educators</h3>
                        <p>Whether you're a new teacher or experienced educator, UCED offers programs to enhance your skills in progressive pedagogy. All programs provide academic credits from Kathmandu University School of Education.</p>
                        <a href="#" class="btn btn-green" style="margin-top: 1rem; display: inline-block;">Enroll Now</a>
                    </div>
                    <div class="content-card">
                        <h3>School Leaders</h3>
                        <p>Principals, administrators, and education leaders can benefit from our specialized leadership development programs and school reform initiatives designed for transformational change.</p>
                        <a href="#" class="btn btn-blue" style="margin-top: 1rem; display: inline-block;">Leadership Programs</a>
                    </div>
                    <div class="content-card">
                        <h3>School Partnerships</h3>
                        <p>Schools can partner with UCED for customized training modules, School Reform Initiative (SRI), and ongoing support tailored to their specific educational goals and contexts.</p>
                        <a href="#" class="btn btn-orange" style="margin-top: 1rem; display: inline-block;">Partner With Us</a>
                    </div>
                    <div class="content-card">
                        <h3>Community Schools</h3>
                        <p>We actively support government and community schools across Nepal through targeted capacity building, mentorship, and ongoing professional development programs.</p>
                        <a href="#" class="btn btn-purple" style="margin-top: 1rem; display: inline-block;">Community Support</a>
                    </div>
                </div>
            </div>
        </section>

        <section id="contact" class="section" style="background: var(--background-gray);">
            <div class="container">
                <h2 class="section-title">Transform Education with UCED</h2>
                <div class="content-grid">
                    <div class="content-card">
                        <h3>Progressive Education Approach</h3>
                        <p>UCED programs are grounded in philosophical and theoretical foundations of progressive teaching, supporting educators in creating student-centered learning environments that nurture holistic development.</p>
                    </div>
                    <div class="content-card">
                        <h3>Practical Application</h3>
                        <p>Our training includes real classroom observation and practice opportunities at Ullens School (K-12), ensuring participants can effectively apply progressive pedagogy in their own teaching contexts.</p>
                    </div>
                    <div class="content-card">
                        <h3>Flexible Programs</h3>
                        <p>From 16-hour leadership intensives to 96-hour comprehensive training programs, UCED offers flexible options to meet the diverse needs of educators at different career stages and with varying time commitments.</p>
                    </div>
                    <div class="content-card">
                        <h3>Systemic Impact</h3>
                        <p>Through our School Reform Initiative and community school support programs, UCED works to create sustainable, systemic change that elevates the quality of education across Nepal.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>UCED</h3>
                    <p>Ullens Center for Educator Development</p>
                    <p>Teacher Training Unit of Ullens Education Foundation</p>
                    <p>In collaboration with Kathmandu University</p>
                </div>
                <div class="footer-section">
                    <h3>Programs</h3>
                    <a href="#programs">Progressive Pedagogy</a>
                    <a href="#programs">Leadership Training</a>
                    <a href="#community">Community Schools</a>
                    <a href="#partnerships">KU Partnership</a>
                </div>
                <div class="footer-section">
                    <h3>Contact UCED</h3>
                    <p>Khumaltar, Lalitpur-15</p>
                    <p>GPO Box. 8975, EPC 1477, Kathmandu, Nepal</p>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +977-1-5230944</p>
                    <p>Fax: +977-1-5570365</p>
                </div>
            </div>
            <div style="border-top: 1px solid #555; padding-top: 2rem; margin-top: 2rem;">
                <p>&copy; 2025 Ullens Center for Educator Development. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <style>
        /* CSS Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* Slider hover effects */
        .slider-prev:hover, .slider-next:hover {
            background: rgba(255,255,255,1) !important;
            transform: translateY(-50%) scale(1.1) !important;
        }

        .slider-dot:hover {
            transform: scale(1.2) !important;
        }

        .slider-dot.active {
            background: #4CAF50 !important;
            transform: scale(1.2);
        }

        /* Counter animation */
        .counter {
            transition: all 0.3s ease;
        }
    </style>

    <script>
        // Slider functionality
        let slideIndex = 1;
        let slideInterval;

        function plusSlides(n) {
            showSlides(slideIndex += n);
        }

        function currentSlide(n) {
            showSlides(slideIndex = n);
        }

        function showSlides(n) {
            const slides = document.querySelector('.slider-container');
            const dots = document.querySelectorAll('.slider-dot');
            const totalSlides = 4;

            if (n > totalSlides) { slideIndex = 1; }
            if (n < 1) { slideIndex = totalSlides; }

            slides.style.transform = `translateX(-${(slideIndex - 1) * 100}%)`;

            dots.forEach(dot => dot.classList.remove('active'));
            dots[slideIndex - 1].classList.add('active');
        }

        // Auto-slide functionality
        function startAutoSlide() {
            slideInterval = setInterval(() => {
                slideIndex++;
                if (slideIndex > 4) slideIndex = 1;
                showSlides(slideIndex);
            }, 5000);
        }

        function stopAutoSlide() {
            clearInterval(slideInterval);
        }

        // Counter animation
        function animateCounters() {
            const counters = document.querySelectorAll('.counter');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / 100;
                let current = 0;

                const updateCounter = () => {
                    if (current < target) {
                        current += increment;
                        counter.textContent = Math.ceil(current);
                        setTimeout(updateCounter, 20);
                    } else {
                        counter.textContent = target;
                    }
                };

                updateCounter();
            });
        }

        // Initialize slider and animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            startAutoSlide();

            // Start counter animation when section comes into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateCounters();
                        observer.unobserve(entry.target);
                    }
                });
            });

            const aboutSection = document.getElementById('about');
            if (aboutSection) {
                observer.observe(aboutSection);
            }

            // Pause auto-slide on hover
            const slider = document.querySelector('.program-slider');
            if (slider) {
                slider.addEventListener('mouseenter', stopAutoSlide);
                slider.addEventListener('mouseleave', startAutoSlide);
            }
        });
    </script>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add scroll effect to header
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.backdropFilter = 'blur(10px)';
            } else {
                header.style.background = 'var(--background-light)';
                header.style.backdropFilter = 'none';
            }
        });
    </script>
</body>
</html>

