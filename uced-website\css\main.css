/* UCED Website - Main Styles */

/* CSS Variables */
:root {
    --primary-green: #4CAF50;
    --primary-blue: #2196F3;
    --primary-orange: #FF9800;
    --primary-purple: #9C27B0;
    --text-dark: #333333;
    --text-light: #666666;
    --text-muted: #999999;
    --background-light: #ffffff;
    --background-gray: #f8f9fa;
    --background-alt: #f1f8e9;
    --border-color: #e0e0e0;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 10px 30px rgba(0, 0, 0, 0.2);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--background-light);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3rem;
    font-weight: 700;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

.lead {
    font-size: 1.2rem;
    font-weight: 400;
    color: var(--text-dark);
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section {
    padding: 4rem 0;
}

.section-alt {
    background-color: var(--background-gray);
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--text-dark);
}

/* Skip Link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-green);
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

/* Header */
.header {
    background: var(--background-light);
    box-shadow: var(--shadow-light);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    transition: var(--transition);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.logo h2 {
    color: var(--primary-green);
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
}

.logo a {
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link:hover,
.nav-link:focus {
    background-color: var(--background-gray);
    color: var(--primary-green);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-green), #66BB6A);
    color: white;
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 6rem 2rem 4rem;
    margin-top: 80px;
}

.hero-content h1 {
    margin-bottom: 1rem;
    animation: fadeInUp 0.8s ease-out;
}

.hero-subtitle {
    font-size: 1.3rem;
    font-weight: 500;
    margin-bottom: 1rem;
    opacity: 0.95;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    min-height: 44px;
    line-height: 1.2;
}

.btn-primary {
    background: white;
    color: var(--primary-green);
}

.btn-primary:hover {
    background: var(--background-gray);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: var(--primary-blue);
    color: white;
}

.btn-secondary:hover {
    background: #1976D2;
    transform: translateY(-2px);
}

.btn-accent {
    background: var(--primary-orange);
    color: white;
}

.btn-accent:hover {
    background: #F57C00;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
}

.btn-outline:hover {
    background: var(--primary-green);
    color: white;
}

/* About Section */
.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    margin-bottom: 4rem;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.8s ease-out;
}

.about-text p:nth-child(2) {
    animation-delay: 0.2s;
    animation-fill-mode: both;
}

.about-text p:nth-child(3) {
    animation-delay: 0.4s;
    animation-fill-mode: both;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stat-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    transition: var(--transition);
    animation: slideInUp 0.8s ease-out;
}

.stat-card:nth-child(1) {
    background: linear-gradient(135deg, #E8F5E8, #F1F8E9);
    animation-delay: 0.1s;
    animation-fill-mode: both;
}

.stat-card:nth-child(2) {
    background: linear-gradient(135deg, #E3F2FD, #F0F8FF);
    animation-delay: 0.2s;
    animation-fill-mode: both;
}

.stat-card:nth-child(3) {
    background: linear-gradient(135deg, #FFF3E0, #FFF8E1);
    animation-delay: 0.3s;
    animation-fill-mode: both;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.stat-number {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.stat-card:nth-child(1) .stat-number {
    color: var(--primary-green);
}

.stat-card:nth-child(2) .stat-number {
    color: var(--primary-blue);
}

.stat-card:nth-child(3) .stat-number {
    color: var(--primary-orange);
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Program Slider */
.program-slider {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: var(--shadow-heavy);
    margin-bottom: 4rem;
}

.slider-container {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.slide {
    min-width: 100%;
    padding: 4rem 3rem;
    text-align: center;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide-content {
    max-width: 600px;
}

.slide-green {
    background: linear-gradient(135deg, var(--primary-green), #66BB6A);
}

.slide-blue {
    background: linear-gradient(135deg, var(--primary-blue), #42A5F5);
}

.slide-orange {
    background: linear-gradient(135deg, var(--primary-orange), #FFB74D);
}

.slide-purple {
    background: linear-gradient(135deg, var(--primary-purple), #BA68C8);
}

.slide h3 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    animation: slideInDown 0.6s ease-out;
}

.slide-meta {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    font-weight: 500;
}

.slide p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: white;
    opacity: 0.95;
    animation: fadeIn 0.8s ease-out 0.3s both;
}

/* Slider Navigation */
.slider-nav {
    text-align: center;
    margin-top: 2rem;
}

.slider-dot {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    border: none;
    background: #ccc;
    margin: 0 5px;
    cursor: pointer;
    transition: var(--transition);
    min-height: 44px;
    min-width: 44px;
}

.slider-dot:hover {
    transform: scale(1.2);
}

.slider-dot.active {
    background: var(--primary-green);
    transform: scale(1.2);
}

.slider-prev,
.slider-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.slider-prev {
    left: 20px;
}

.slider-next {
    right: 20px;
}

.slider-prev:hover,
.slider-next:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-50%) scale(1.1);
}

/* Additional Programs */
.additional-programs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
}

.program-card {
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.program-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.program-card h3 {
    color: var(--primary-green);
    margin-bottom: 1rem;
}

.program-meta {
    font-weight: bold;
    color: var(--text-light);
    margin-bottom: 1rem;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.content-card {
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.content-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.content-card h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.content-card .btn {
    margin-top: 1rem;
}

/* Timeline Styles */
.timeline-desktop {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    display: none;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, var(--primary-green), #66BB6A);
    transform: translateX(-50%);
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-left {
    animation: slideInLeft 0.8s ease-out;
}

.timeline-right {
    animation: slideInRight 0.8s ease-out 0.2s both;
}

.timeline-left .timeline-content {
    flex: 1;
    text-align: right;
    padding-right: 2rem;
}

.timeline-right .timeline-content {
    flex: 1;
    text-align: left;
    padding-left: 2rem;
}

.timeline-right {
    flex-direction: row-reverse;
}

.timeline-content h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.timeline-meta {
    font-weight: bold;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.timeline-dot {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 4px solid white;
    z-index: 1;
    position: relative;
}

.timeline-dot-green {
    background: var(--primary-green);
    box-shadow: 0 0 0 4px var(--primary-green);
}

.timeline-dot-blue {
    background: var(--primary-blue);
    box-shadow: 0 0 0 4px var(--primary-blue);
}

.timeline-dot-orange {
    background: var(--primary-orange);
    box-shadow: 0 0 0 4px var(--primary-orange);
}

.timeline-dot-purple {
    background: var(--primary-purple);
    box-shadow: 0 0 0 4px var(--primary-purple);
}

/* Mobile Timeline */
.timeline-mobile {
    display: block;
    position: relative;
    padding-left: 2rem;
}

.timeline-mobile::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-green), #66BB6A);
}

.mobile-timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 1rem;
}

.mobile-timeline-item::before {
    content: '';
    position: absolute;
    left: -0.6rem;
    top: 0.5rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--primary-green);
    border: 2px solid white;
    box-shadow: 0 0 0 2px var(--primary-green);
}

.mobile-timeline-item h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: var(--primary-green);
}

.mobile-timeline-meta {
    font-weight: bold;
    color: var(--text-light);
    margin-bottom: 1rem;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: white;
    margin-bottom: 1rem;
}

.footer-section p {
    color: #ccc;
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    display: block;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-green);
}

.social-links {
    margin-top: 1rem;
}

.social-links a {
    display: inline-block;
    margin-right: 1rem;
    color: var(--primary-green);
    font-weight: 500;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 2rem;
    text-align: center;
}

.footer-bottom p {
    color: #ccc;
    margin: 0;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Focus Styles for Accessibility */
.nav-link:focus,
.btn:focus,
.slider-prev:focus,
.slider-next:focus,
.slider-dot:focus {
    outline: 2px solid var(--primary-green);
    outline-offset: 2px;
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
